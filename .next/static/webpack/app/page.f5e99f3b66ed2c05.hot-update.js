"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/footer.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Footer() {\n    const handleNavClick = (sectionId)=>{\n        const section = document.getElementById(sectionId);\n        if (section) {\n            section.scrollIntoView({\n                behavior: 'smooth',\n                block: 'start'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-foreground text-background py-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 lg:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/logo.png\",\n                                            alt: \"HomeNest Logo\",\n                                            className: \"w-10 h-10 rounded-lg object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-xl\",\n                                            children: \"STEM Kids Workshop\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-background/70\",\n                                    children: \"Fun, hands-on STEM learning for kids aged 5-10. Small groups, big discoveries, and unforgettable learning experiences.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-primary/20 p-4 rounded-lg mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-background font-semibold\",\n                                        children: \"\\uD83C\\uDFAF AED 75 per child - All materials included!\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-background/70 hover:text-background transition-colors\",\n                                            \"aria-label\": \"Facebook\",\n                                            \"data-testid\": \"link-facebook\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-background/70 hover:text-background transition-colors\",\n                                            \"aria-label\": \"Instagram\",\n                                            \"data-testid\": \"link-instagram\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-lg mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-background/70\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleNavClick('about'),\n                                                className: \"hover:text-background transition-colors\",\n                                                \"data-testid\": \"footer-about\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleNavClick('programs'),\n                                                className: \"hover:text-background transition-colors\",\n                                                \"data-testid\": \"footer-programs\",\n                                                children: \"Programs\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleNavClick('testimonials'),\n                                                className: \"hover:text-background transition-colors\",\n                                                \"data-testid\": \"footer-testimonials\",\n                                                children: \"Testimonials\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleNavClick('contact'),\n                                                className: \"hover:text-background transition-colors\",\n                                                \"data-testid\": \"footer-contact\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-lg mb-4\",\n                                    children: \"Programs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-background/70\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hover:text-background transition-colors\",\n                                                children: \"Elementary Education\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hover:text-background transition-colors\",\n                                                children: \"Middle School\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hover:text-background transition-colors\",\n                                                children: \"STEM Excellence\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hover:text-background transition-colors\",\n                                                children: \"Creative Arts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-lg mb-4\",\n                                    children: \"Contact Info\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-background/70\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            \"data-testid\": \"text-address\",\n                                            children: [\n                                                \"123 Learning Lane\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 62\n                                                }, this),\n                                                \"Education City, EC 12345\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            \"data-testid\": \"text-phone\",\n                                            children: \"(555) 123-BRIGHT\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            \"data-testid\": \"text-email\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-background/20 mt-12 pt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-background/70\",\n                        children: [\n                            \"\\xa9 2024 BrightMinds Academy. All rights reserved. |\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"hover:text-background transition-colors ml-1\",\n                                \"data-testid\": \"link-privacy\",\n                                children: \"Privacy Policy\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            \" |\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"hover:text-background transition-colors ml-1\",\n                                \"data-testid\": \"link-terms\",\n                                children: \"Terms of Service\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/footer.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/footer.tsx\n"));

/***/ })

});