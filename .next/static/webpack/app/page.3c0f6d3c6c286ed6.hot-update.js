"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Header() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEnrollClick = ()=>{\n        const contactSection = document.getElementById('contact');\n        if (contactSection) {\n            contactSection.scrollIntoView({\n                behavior: 'smooth',\n                block: 'start'\n            });\n        }\n    };\n    const handleNavClick = (sectionId)=>{\n        const section = document.getElementById(sectionId);\n        if (section) {\n            section.scrollIntoView({\n                behavior: 'smooth',\n                block: 'start'\n            });\n        }\n        setIsMenuOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container mx-auto px-4 lg:px-6 py-4\",\n            \"aria-label\": \"Main navigation\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo.png\",\n                                    alt: \"HomeNest Logo\",\n                                    className: \"w-10 h-10 rounded-lg object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold text-xl text-foreground\",\n                                    children: \"STEM Kids Workshop\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleNavClick('activities'),\n                                    className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                    \"data-testid\": \"nav-activities\",\n                                    children: \"Activities\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleNavClick('details'),\n                                    className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                    \"data-testid\": \"nav-details\",\n                                    children: \"Details\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleNavClick('testimonials'),\n                                    className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                    \"data-testid\": \"nav-testimonials\",\n                                    children: \"Educator\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleNavClick('faq'),\n                                    className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                    \"data-testid\": \"nav-faq\",\n                                    children: \"FAQ\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.open('https://wa.me/971501234567?text=Hi! I want to book a STEM workshop for my child', '_blank'),\n                                    className: \"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium\",\n                                    \"data-testid\": \"button-book-header\",\n                                    children: \"Book Now\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"md:hidden p-2\",\n                                    \"aria-label\": \"Toggle menu\",\n                                    onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                    \"data-testid\": \"button-mobile-menu\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden mt-4 py-4 border-t border-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleNavClick('activities'),\n                                className: \"text-muted-foreground hover:text-foreground transition-colors text-left\",\n                                \"data-testid\": \"nav-activities-mobile\",\n                                children: \"Activities\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleNavClick('details'),\n                                className: \"text-muted-foreground hover:text-foreground transition-colors text-left\",\n                                \"data-testid\": \"nav-details-mobile\",\n                                children: \"Details\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleNavClick('testimonials'),\n                                className: \"text-muted-foreground hover:text-foreground transition-colors text-left\",\n                                \"data-testid\": \"nav-testimonials-mobile\",\n                                children: \"Educator\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleNavClick('faq'),\n                                className: \"text-muted-foreground hover:text-foreground transition-colors text-left\",\n                                \"data-testid\": \"nav-faq-mobile\",\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/HomeNest/src/components/layout/header.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"vK10R+uCyHfZ4DZVnxbYkMWJB8g=\");\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/header.tsx\n"));

/***/ })

});