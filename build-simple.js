#!/usr/bin/env node

import { build } from 'esbuild';
import { readFileSync, writeFileSync, mkdirSync, copyFileSync, readdirSync, statSync } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Copy directory recursively
function copyDir(src, dest) {
  mkdirSync(dest, { recursive: true });
  const entries = readdirSync(src, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      copyDir(srcPath, destPath);
    } else {
      copyFileSync(srcPath, destPath);
    }
  }
}

async function buildProject() {
  try {
    console.log('Building STEM Kids Workshop with esbuild...');
    
    // Create output directory
    mkdirSync(path.resolve(__dirname, 'dist/public'), { recursive: true });
    
    // Copy public assets
    const publicDir = path.resolve(__dirname, 'public');
    const outputDir = path.resolve(__dirname, 'dist/public');
    
    if (readdirSync(publicDir).length > 0) {
      copyDir(publicDir, outputDir);
    }
    
    // Build with esbuild
    await build({
      entryPoints: [path.resolve(__dirname, 'client/src/main.tsx')],
      bundle: true,
      outfile: path.resolve(__dirname, 'dist/public/assets/index.js'),
      format: 'esm',
      target: 'esnext',
      minify: true,
      sourcemap: false,
      define: {
        'process.env.NODE_ENV': '"production"',
        global: 'globalThis',
      },
      loader: {
        '.tsx': 'tsx',
        '.ts': 'ts',
        '.css': 'css',
        '.png': 'file',
        '.jpg': 'file',
        '.jpeg': 'file',
        '.svg': 'file',
      },
      external: [],
    });
    
    // Create HTML file
    const htmlTemplate = `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.jpeg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>STEM Kids Workshop - Fun Learning for Ages 5-10</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/assets/index.js"></script>
  </body>
</html>`;
    
    writeFileSync(path.resolve(__dirname, 'dist/public/index.html'), htmlTemplate);
    
    console.log('Build completed successfully with esbuild!');
  } catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
  }
}

buildProject();
