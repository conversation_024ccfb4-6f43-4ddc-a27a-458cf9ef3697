<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>BrightMinds Academy - Nurturing Young Learners for Tomorrow</title>
    <meta name="description" content="Premier educational program for children ages 5-15. Personalized learning, creative development, and academic excellence in a supportive environment." />
    
    <!-- Open Graph -->
    <meta property="og:title" content="BrightMinds Academy - Nurturing Young Learners" />
    <meta property="og:description" content="Premier educational program for children ages 5-15. Personalized learning and creative development." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://brightmindsacademy.com" />
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="BrightMinds Academy - Nurturing Young Learners" />
    <meta name="twitter:description" content="Premier educational program for children ages 5-15. Personalized learning and creative development." />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- JSON-LD Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "EducationalOrganization",
        "name": "BrightMinds Academy",
        "description": "Premier educational program for children ages 5-15. Personalized learning, creative development, and academic excellence.",
        "url": "https://brightmindsacademy.com",
        "telephone": "******-BRIGHT",
        "address": {
            "@type": "PostalAddress",
            "addressLocality": "Education City",
            "addressRegion": "State",
            "postalCode": "12345",
            "addressCountry": "US"
        },
        "sameAs": [
            "https://facebook.com/brightmindsacademy",
            "https://instagram.com/brightmindsacademy"
        ]
    }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
